[{"index": 0, "name": "Xtreme", "weight": 500, "value": 400, "parts": {"engine": {"name": "V12 Hypercar", "horsepower": 900, "weight": 350, "value": 12000, "category": "engine"}, "turbo": {"name": "Twin Turbo Setup", "horsepower_boost_percentage": 60, "weight": 70, "value": 3200, "category": "turbo"}, "intercooler": {"name": "Race Spec Intercooler", "horsepower_boost_percentage": 18, "weight": 18, "value": 1200, "category": "intercooler"}, "ecu": {"name": "Pro Tuner ECU", "horsepower_boost_percentage": 65, "weight": 3, "value": 6000, "category": "ecu"}}}, {"index": 1, "name": "future", "weight": 450, "value": 800, "parts": {"engine": {"name": "Electric Motor", "horsepower": 600, "weight": 200, "value": 8000, "category": "engine"}, "turbo": null, "intercooler": {"name": "Cooling System", "horsepower_boost_percentage": 10, "weight": 25, "value": 1500, "category": "intercooler"}, "ecu": {"name": "AI Control Unit", "horsepower_boost_percentage": 40, "weight": 5, "value": 5000, "category": "ecu"}}}, {"index": 2, "name": "old", "weight": 600, "value": 200, "parts": {"engine": {"name": "Classic V8", "horsepower": 300, "weight": 400, "value": 2000, "category": "engine"}, "turbo": null, "intercooler": null, "ecu": {"name": "Basic ECU", "horsepower_boost_percentage": 5, "weight": 3, "value": 200, "category": "ecu"}}}]