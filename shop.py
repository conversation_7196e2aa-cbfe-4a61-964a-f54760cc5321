import pygame
import json
from color_system import ColorSystem
import time
from valuation_system import valuation_system

class ShopCard:
    def __init__(self, item_data, x, y, width, height, item_type="car", is_owned=False):
        self.item_data = item_data
        self.rect = pygame.Rect(x, y, width, height)
        self.item_type = item_type
        self.is_hovered = False
        self.is_owned = is_owned
        self.font = pygame.font.SysFont("arial", 24)
        self.header_font = pygame.font.SysFont("arial", 32)
        
        # Load item image if it's a car
        self.item_image = None
        if item_type == "car":
            self.load_car_image()
    
    def load_car_image(self):
        try:
            # Use new color system to load and colorize car image
            self.item_image = ColorSystem.load_and_colorize_car_image(
                self.item_data['name'],
                "0",  # Default to first color in palette
                size=(150, 100)
            )
        except Exception as e:
            print(f"Error loading car image in shop: {e}")
            self.item_image = pygame.Surface((150, 100))
            self.item_image.fill((100, 100, 100))
    
    def update(self, mouse_pos, mouse_click):
        self.is_hovered = self.rect.collidepoint(mouse_pos)
        if self.is_hovered and mouse_click[0]:
            return True
        return False
    
    def draw(self, screen):
        # Draw card background
        if self.is_owned:
            bg_color = (20, 80, 20) if self.is_hovered else (10, 60, 10)
            border_color = (0, 255, 0)
        else:
            bg_color = (60, 60, 60) if self.is_hovered else (40, 40, 40)
            border_color = (200, 200, 200)
        
        pygame.draw.rect(screen, bg_color, self.rect)
        pygame.draw.rect(screen, border_color, self.rect, 1)
        
        # Draw item image or icon
        if self.item_image:
            image_x = self.rect.x + (self.rect.width - self.item_image.get_width()) // 2
            image_y = self.rect.y + 10
            screen.blit(self.item_image, (image_x, image_y))
        
        # Draw item info
        y_offset = self.rect.y + (120 if self.item_type == "car" else 20)
        
        # Item name
        name = self.item_data.get("name", "Unknown")
        name_color = (150, 255, 150) if self.is_owned else (255, 255, 255)
        name_text = self.header_font.render(name.title(), True, name_color)
        screen.blit(name_text, (self.rect.x + 10, y_offset))
        y_offset += 35
        
        # Show "OWNED" label if owned
        if self.is_owned:
            owned_text = self.font.render("POSIADANE", True, (0, 255, 0))
            screen.blit(owned_text, (self.rect.x + 10, y_offset))
            y_offset += 25
        
        # Draw stats based on item type
        if self.item_type == "car":
            # Find engine part in parts dict
            engine_part = self.item_data['parts'].get('engine', None)
            horsepower = engine_part['horsepower'] if engine_part else 'N/A'
            stats = [
                f"Waga: {self.item_data['weight']} kg",
                f"Wartość: {self.item_data['value']} $",
                f"Moc: {horsepower} KM"
            ]
        else:
            stats = []
            if "horsepower" in self.item_data:
                stats.append(f"Moc: {self.item_data['horsepower']} KM")
            if "horsepower_boost_percentage" in self.item_data:
                stats.append(f"Boost: +{self.item_data['horsepower_boost_percentage']}%")
            if "weight" in self.item_data:
                stats.append(f"Waga: {self.item_data['weight']} kg")
            stats.append(f"Cena: {self.item_data['value']} $")
        
        for stat in stats:
            stat_text = self.font.render(stat, True, (200, 200, 200))
            screen.blit(stat_text, (self.rect.x + 10, y_offset))
            y_offset += 25

class TabButton:
    def __init__(self, label, x, y, width, height):
        self.rect = pygame.Rect(x, y, width, height)
        self.label = label
        self.is_active = False
        self.is_hovered = False
        self.font = pygame.font.SysFont("arial", 32)
    
    def update(self, mouse_pos, mouse_click):
        self.is_hovered = self.rect.collidepoint(mouse_pos)
        if self.is_hovered and mouse_click[0]:
            return True
        return False
    
    def draw(self, screen):
        # Draw background
        if self.is_active:
            bg_color = (0, 255, 255)
            text_color = (0, 0, 0)
        elif self.is_hovered:
            bg_color = (80, 80, 80)
            text_color = (255, 255, 255)
        else:
            bg_color = (50, 50, 50)
            text_color = (200, 200, 200)
        
        pygame.draw.rect(screen, bg_color, self.rect)
        pygame.draw.rect(screen, (200, 200, 200), self.rect, 1)
        
        # Draw label
        text = self.font.render(self.label, True, text_color)
        text_x = self.rect.x + (self.rect.width - text.get_width()) // 2
        text_y = self.rect.y + (self.rect.height - text.get_height()) // 2
        screen.blit(text, (text_x, text_y))

class ColorSelectionDialog:
    def __init__(self, car_data, s_width, s_height):
        self.width = 600
        self.height = 400
        self.rect = pygame.Rect(
            (s_width - self.width) // 2,
            (s_height - self.height) // 2,
            self.width,
            self.height
        )
        self.car_data = car_data
        self.font = pygame.font.SysFont("arial", 24)
        self.header_font = pygame.font.SysFont("arial", 32)
        self.selected_color_index = "0"  # Default to first color in palette

        # Create color swatches using new color system
        self.color_swatches = []
        available_colors = ColorSystem.get_available_colors()
        swatch_size = 50
        spacing = 15
        colors_per_row = 6
        start_y = self.rect.y + 120

        # Calculate starting x position to center the swatches
        total_width = min(len(available_colors), colors_per_row) * (swatch_size + spacing) - spacing
        start_x = self.rect.x + (self.width - total_width) // 2

        for i, color_index in enumerate(available_colors):
            row = i // colors_per_row
            col = i % colors_per_row
            x = start_x + col * (swatch_size + spacing)
            y = start_y + row * (swatch_size + spacing)

            swatch_rect = pygame.Rect(x, y, swatch_size, swatch_size)
            self.color_swatches.append((color_index, swatch_rect))

        # Create action buttons
        button_y = self.rect.y + self.height - 60
        self.confirm_button = pygame.Rect(self.rect.x + 50, button_y, 120, 40)
        self.cancel_button = pygame.Rect(self.rect.x + self.width - 170, button_y, 120, 40)

    def update(self, mouse_pos, mouse_click):
        if mouse_click[0]:
            # Check color swatches
            for color_index, swatch_rect in self.color_swatches:
                if swatch_rect.collidepoint(mouse_pos):
                    self.selected_color_index = color_index
                    return None

            # Check action buttons
            if self.confirm_button.collidepoint(mouse_pos):
                return {"action": "confirm", "color": self.selected_color_index}
            elif self.cancel_button.collidepoint(mouse_pos):
                return {"action": "cancel"}
        return None

    def draw(self, screen):
        # Draw dialog background
        pygame.draw.rect(screen, (40, 40, 40), self.rect)
        pygame.draw.rect(screen, (200, 200, 200), self.rect, 2)

        # Draw header
        header_text = self.header_font.render("Wybierz kolor pojazdu", True, (255, 255, 255))
        header_x = self.rect.x + (self.width - header_text.get_width()) // 2
        screen.blit(header_text, (header_x, self.rect.y + 20))

        # Draw car name
        car_text = self.font.render(f"Model: {self.car_data['name'].title()}", True, (200, 200, 200))
        car_x = self.rect.x + (self.width - car_text.get_width()) // 2
        screen.blit(car_text, (car_x, self.rect.y + 60))

        # Draw color swatches
        for color_index, swatch_rect in self.color_swatches:
            # Create and draw color swatch
            color_swatch = ColorSystem.create_color_swatch(color_index, (swatch_rect.width, swatch_rect.height))
            screen.blit(color_swatch, swatch_rect)

            # Highlight selected color
            if color_index == self.selected_color_index:
                pygame.draw.rect(screen, (255, 255, 0), swatch_rect, 4)  # Yellow border for selected

            # Show color name on hover
            mouse_pos = pygame.mouse.get_pos()
            if swatch_rect.collidepoint(mouse_pos):
                color_name = ColorSystem.get_color_name(color_index)
                name_text = self.font.render(color_name, True, (255, 255, 255))
                name_bg = pygame.Rect(swatch_rect.x, swatch_rect.y - 30, name_text.get_width() + 10, name_text.get_height() + 5)
                pygame.draw.rect(screen, (0, 0, 0, 180), name_bg)
                screen.blit(name_text, (swatch_rect.x + 5, swatch_rect.y - 27))

        # Draw price info
        price_text = self.font.render(f"Cena: {self.car_data['value']} $", True, (255, 255, 0))
        price_x = self.rect.x + (self.width - price_text.get_width()) // 2
        screen.blit(price_text, (price_x, self.rect.y + 220))

        # Draw action buttons
        for button, text in [(self.confirm_button, "Kup"), (self.cancel_button, "Anuluj")]:
            pygame.draw.rect(screen, (60, 60, 60), button)
            pygame.draw.rect(screen, (200, 200, 200), button, 1)

            button_text = self.font.render(text, True, (255, 255, 255))
            text_x = button.x + (button.width - button_text.get_width()) // 2
            text_y = button.y + (button.height - button_text.get_height()) // 2
            screen.blit(button_text, (text_x, text_y))

class ConfirmDialog:
    def __init__(self, item_name, price, s_width, s_height):
        self.width = 400
        self.height = 200
        self.rect = pygame.Rect(
            (s_width - self.width) // 2,
            (s_height - self.height) // 2,
            self.width,
            self.height
        )
        self.item_name = item_name
        self.price = price
        self.font = pygame.font.SysFont("arial", 24)

        # Create buttons
        button_y = self.rect.y + self.height - 60
        self.confirm_button = pygame.Rect(self.rect.x + 50, button_y, 120, 40)
        self.cancel_button = pygame.Rect(self.rect.x + self.width - 170, button_y, 120, 40)
    
    def update(self, mouse_pos, mouse_click):
        if mouse_click[0]:
            if self.confirm_button.collidepoint(mouse_pos):
                return "confirm"
            elif self.cancel_button.collidepoint(mouse_pos):
                return "cancel"
        return None
    
    def draw(self, screen):
        # Draw dialog background
        pygame.draw.rect(screen, (40, 40, 40), self.rect)
        pygame.draw.rect(screen, (200, 200, 200), self.rect, 2)
        
        # Draw text
        title = self.font.render(f"Kup {self.item_name}?", True, (255, 255, 255))
        price = self.font.render(f"Cena: {self.price} $", True, (200, 200, 200))
        
        screen.blit(title, (self.rect.x + (self.width - title.get_width()) // 2, self.rect.y + 30))
        screen.blit(price, (self.rect.x + (self.width - price.get_width()) // 2, self.rect.y + 70))
        
        # Draw buttons
        for button, text in [(self.confirm_button, "Kup"), (self.cancel_button, "Anuluj")]:
            pygame.draw.rect(screen, (60, 60, 60), button)
            pygame.draw.rect(screen, (200, 200, 200), button, 1)
            
            button_text = self.font.render(text, True, (255, 255, 255))
            text_x = button.x + (button.width - button_text.get_width()) // 2
            text_y = button.y + (button.height - button_text.get_height()) // 2
            screen.blit(button_text, (text_x, text_y))

def initialize_new_car_systems(car_index, profile_data):
    """Initialize all system data for a newly purchased car"""
    car_key = str(car_index)
    current_time = int(time.time())

    # Initialize usage_data
    if "usage_data" not in profile_data:
        profile_data["usage_data"] = {"cars": {}}
    if "cars" not in profile_data["usage_data"]:
        profile_data["usage_data"]["cars"] = {}

    if car_key not in profile_data["usage_data"]["cars"]:
        profile_data["usage_data"]["cars"][car_key] = valuation_system.get_default_usage_data()
        profile_data["usage_data"]["cars"][car_key]["last_update"] = current_time

    # Initialize fuel_data
    if "fuel_data" not in profile_data:
        profile_data["fuel_data"] = {"cars": {}}
    if "cars" not in profile_data["fuel_data"]:
        profile_data["fuel_data"]["cars"] = {}

    if car_key not in profile_data["fuel_data"]["cars"]:
        profile_data["fuel_data"]["cars"][car_key] = {
            "current_fuel": 60,  # Start with full tank
            "max_capacity": 60,
            "last_refuel": current_time
        }

    # Initialize tire_data
    if "tire_data" not in profile_data:
        profile_data["tire_data"] = {"cars": {}}
    if "cars" not in profile_data["tire_data"]:
        profile_data["tire_data"]["cars"] = {}

    if car_key not in profile_data["tire_data"]["cars"]:
        profile_data["tire_data"]["cars"][car_key] = {
            "tire_type": "standard",
            "condition": 100.0,
            "total_distance": 0.0,
            "last_replacement": current_time
        }

    # Initialize maintenance_data
    if "maintenance_data" not in profile_data:
        profile_data["maintenance_data"] = {"cars": {}, "insurance": None}
    if "cars" not in profile_data["maintenance_data"]:
        profile_data["maintenance_data"]["cars"] = {}

    if car_key not in profile_data["maintenance_data"]["cars"]:
        profile_data["maintenance_data"]["cars"][car_key] = {
            "last_maintenance": current_time,
            "last_maintenance_races": 0,
            "maintenance_due": False,
            "total_maintenance_cost": 0,
            "crashes": 0,
            "repair_history": []
        }

def save_purchase(item_data, item_type, selected_color=None):
    """Save the purchase to profile.json and update garage.json"""
    try:
        with open('data/profile.json', 'r') as f:
            profile = json.load(f)
    except FileNotFoundError:
        raise ValueError("Profile file not found. Please restart the game.")
    except json.JSONDecodeError:
        raise ValueError("Profile file is corrupted. Please restart the game.")
    except Exception as e:
        raise ValueError(f"Error loading profile: {e}")

    # Validate item data
    if not item_data or 'value' not in item_data or 'name' not in item_data:
        raise ValueError("Invalid item data.")

    if item_data['value'] <= 0:
        raise ValueError("Invalid item price.")

    # Check if user has enough money
    if profile['money'] < item_data['value']:
        raise ValueError("Not enough money to buy this item.")

    # Deduct money
    profile['money'] -= item_data['value']

    # Add item to inventory
    if item_type == "car":
        if item_data["name"] not in profile["inventory"]["owned_cars"]:
            profile["inventory"]["owned_cars"].append(item_data["name"])
            # Update selected_car to the index of the newly bought car
            car_index = len(profile["inventory"]["owned_cars"]) - 1
            profile["cars"]["selected_car"] = car_index

            # Initialize car_colors if it doesn't exist
            if "car_colors" not in profile["cars"]:
                profile["cars"]["car_colors"] = {}

            # Set the selected color for this car
            if selected_color:
                profile["cars"]["car_colors"][str(car_index)] = selected_color
            else:
                # Default to first available color from color system
                available_colors = ColorSystem.get_available_colors()
                profile["cars"]["car_colors"][str(car_index)] = available_colors[0] if available_colors else "0"

            # Initialize all system data for the new car
            initialize_new_car_systems(car_index, profile)

            # Update garage.json to include the new car
            try:
                with open('data/garage.json', 'r') as gf:
                    garage_data = json.load(gf)
            except FileNotFoundError:
                garage_data = []
            except json.JSONDecodeError:
                raise ValueError("Garage file is corrupted. Please restart the game.")
            except Exception as e:
                raise ValueError(f"Error loading garage: {e}")

            # Check if car already in garage_data
            car_names_in_garage = [car['name'] for car in garage_data]
            if item_data["name"] not in car_names_in_garage:
                try:
                    # Load shop_data to get full car data
                    with open('data/shop_data.json', 'r') as sf:
                        shop_data = json.load(sf)
                    cars_list = shop_data[1].get('cars', [])
                    # Find the car data by name
                    car_to_add = next((car for car in cars_list if car['name'] == item_data["name"]), None)
                    if car_to_add:
                        garage_data.append(car_to_add)
                        with open('data/garage.json', 'w') as gf:
                            json.dump(garage_data, gf, indent=4)
                    else:
                        raise ValueError(f"Car '{item_data['name']}' not found in shop data.")
                except Exception as e:
                    raise ValueError(f"Error updating garage: {e}")
    else:  # part
        # Validate part data
        if "category" not in item_data:
            raise ValueError("Part data missing category information.")

        category = item_data["category"]

        # Ensure the category exists in profile
        if "inventory" not in profile:
            profile["inventory"] = {"owned_cars": [], "owned_parts": {}}
        if "owned_parts" not in profile["inventory"]:
            profile["inventory"]["owned_parts"] = {}
        if category not in profile["inventory"]["owned_parts"]:
            profile["inventory"]["owned_parts"][category] = []

        # Add part if not already owned
        if item_data["name"] not in profile["inventory"]["owned_parts"][category]:
            profile["inventory"]["owned_parts"][category].append(item_data["name"])

    try:
        with open('data/profile.json', 'w') as f:
            json.dump(profile, f, indent=4)
    except Exception as e:
        raise ValueError(f"Error saving profile: {e}")

    # Auto-save if current save slot is set
    try:
        from save_system import save_system
        if save_system.current_save_slot:
            save_system.save_game(save_system.current_save_slot)
    except Exception as e:
        print(f"Warning: Auto-save failed: {e}")
