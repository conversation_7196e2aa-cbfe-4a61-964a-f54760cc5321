
import pygame
from color_system import ColorSystem

class CarCard:
    def __init__(self, car_data, x, y, width, height, is_selected=False):
        self.car_data = car_data
        self.rect = pygame.Rect(x, y, width, height)
        self.is_selected = is_selected
        self.is_hovered = False
        self.selected_color_index = "0"  # Default color
        self.font = pygame.font.SysFont("arial", 24)
        self.header_font = pygame.font.SysFont("arial", 32)
        
        # Load car image
        self.car_image = None
        self.load_car_image()
        
    def load_car_image(self):
        try:
            # Handle case where selected_color_index is None
            if self.selected_color_index is None:
                # Use the first available color as default
                available_colors = ColorSystem.get_available_colors()
                if available_colors:
                    self.selected_color_index = available_colors[0]
                else:
                    self.selected_color_index = "0"  # fallback to red

            # Use new color system to load and colorize car image
            self.car_image = ColorSystem.load_and_colorize_car_image(
                self.car_data['name'],
                self.selected_color_index,
                size=(150, 100)
            )
        except Exception as e:
            print(f"Error loading car image: {e}")
            # Create a placeholder rectangle if image not found
            self.car_image = pygame.Surface((150, 100))
            self.car_image.fill((100, 100, 100))
    
    def update(self, mouse_pos, mouse_click):
        self.is_hovered = self.rect.collidepoint(mouse_pos)
        if self.is_hovered and mouse_click[0]:
            return True  # Card was clicked
        return False
    
    def set_color(self, color_index):
        # Handle None color_index by using first available color
        if color_index is None:
            available_colors = ColorSystem.get_available_colors()
            if available_colors:
                self.selected_color_index = available_colors[0]
            else:
                self.selected_color_index = "0"  # fallback
        else:
            self.selected_color_index = color_index
        self.load_car_image()
    
    def draw(self, screen):
        # Draw card background
        bg_color = (80, 80, 80) if self.is_selected else (50, 50, 50)
        if self.is_hovered:
            bg_color = (100, 100, 100)
        
        pygame.draw.rect(screen, bg_color, self.rect)
        
        # Draw border
        border_color = (0, 255, 255) if self.is_selected else (200, 200, 200)
        border_width = 3 if self.is_selected else 1
        pygame.draw.rect(screen, border_color, self.rect, border_width)
        
        # Draw car image
        if self.car_image:
            image_x = self.rect.x + (self.rect.width - self.car_image.get_width()) // 2
            image_y = self.rect.y + 10
            screen.blit(self.car_image, (image_x, image_y))
        
        # Draw car info
        y_offset = self.rect.y + 120
        
        # Car name
        name_text = self.header_font.render(self.car_data["name"].title(), True, (255, 255, 255))
        screen.blit(name_text, (self.rect.x + 10, y_offset))
        y_offset += 35
        
        # Car stats
        # Handle case where selected_color_index is None
        if self.selected_color_index is None:
            # Use the first available color as default
            available_colors = list(self.car_data["color"].keys())
            if available_colors:
                self.selected_color_index = available_colors[0]

        try:
            color_name = self.car_data["color"][self.selected_color_index]
        except (KeyError, TypeError):
            color_name = "Unknown"

        color_text = self.font.render(f"Kolor: {color_name.title()}", True, (200, 200, 200))
        screen.blit(color_text, (self.rect.x + 10, y_offset))
        y_offset += 25
        
        weight_text = self.font.render(f"Waga: {self.car_data['weight']} kg", True, (200, 200, 200))
        screen.blit(weight_text, (self.rect.x + 10, y_offset))
        y_offset += 25
        
        value_text = self.font.render(f"Wartość: {self.car_data['value']} $", True, (200, 200, 200))
        screen.blit(value_text, (self.rect.x + 10, y_offset))
        y_offset += 25
        
        # Calculate total horsepower: engine base + sum of horsepower_boost from mounted parts
        engine_part = self.car_data['parts'].get('engine')
        total_horsepower = engine_part.get('horsepower', 0) if engine_part else 0
        for part_key, part in self.car_data['parts'].items():
            if part_key != 'engine' and part is not None:
                horsepower_boost = part.get('horsepower_boost', 0)
                total_horsepower += horsepower_boost
        
        hp_text = self.font.render(f"Moc: {total_horsepower} KM", True, (200, 200, 200))
        screen.blit(hp_text, (self.rect.x + 10, y_offset))

class TextButton:
    def __init__(self, label, x, y, font_name='arial', font_size=48, action=None, width=None, height=None):
        self.label = label
        self.x = x
        self.y = y
        self.font = pygame.font.SysFont(font_name, font_size)
        self.default_color = (200, 200, 200)
        self.hover_color = (0, 255, 255)
        self.action = action
        self.is_hovered = False
        self.width = width
        self.height = height
        self.render()

    def render(self):
        color = self.hover_color if self.is_hovered else self.default_color
        self.surface = self.font.render(self.label, True, color)

        # If width and height are specified, create a button background
        if self.width and self.height:
            # Create a surface for the button background
            button_surface = pygame.Surface((self.width, self.height))
            button_surface.fill((50, 50, 50))  # Dark gray background

            # Add border
            border_color = self.hover_color if self.is_hovered else (100, 100, 100)
            pygame.draw.rect(button_surface, border_color, (0, 0, self.width, self.height), 2)

            # Center the text on the button
            text_rect = self.surface.get_rect(center=(self.width // 2, self.height // 2))
            button_surface.blit(self.surface, text_rect)

            self.surface = button_surface
            self.rect = pygame.Rect(self.x, self.y, self.width, self.height)
        else:
            # Original behavior for backward compatibility
            self.rect = self.surface.get_rect(topleft=(self.x, self.y))

    def update(self, mouse_pos, mouse_click):
        self.is_hovered = self.rect.collidepoint(mouse_pos)
        self.render()
        if self.is_hovered and mouse_click[0] and self.action:
            self.action()

    def draw(self, screen):
        screen.blit(self.surface, self.rect)
